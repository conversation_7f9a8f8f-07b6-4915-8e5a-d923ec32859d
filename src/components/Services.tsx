'use client';

const services = [
  {
    icon: '📱',
    title: 'طراحی اپلیکیشن موبایل',
    description: 'توسعه اپلیکیشن‌های موبایل حرفه‌ای برای اندروید و iOS'
  },
  {
    icon: '🌐',
    title: 'طراحی وب‌سایت',
    description: 'ساخت وب‌سایت‌های مدرن و ریسپانسیو با بهترین تکنولوژی‌ها'
  },
  {
    icon: '🎨',
    title: 'طراحی UI/UX',
    description: 'طراحی رابط کاربری زیبا و تجربه کاربری بهینه'
  },
  {
    icon: '⚙️',
    title: 'توسعه نرم‌افزار',
    description: 'ساخت نرم‌افزارهای تحت وب و دسکتاپ سفارشی'
  }
];

export default function Services() {
  return (
    <section id="services" className="services">
      <div className="container">
        <h2 className="section-title">خدمات ما</h2>

        <div className="services-grid">
          {services.map((service, index) => (
            <div key={index} className="service-card">
              <div className="service-icon">
                {service.icon}
              </div>
              <h3>{service.title}</h3>
              <p>{service.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
