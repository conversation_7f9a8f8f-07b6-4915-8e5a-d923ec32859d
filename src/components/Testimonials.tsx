'use client';

const testimonials = [
  {
    text: 'تیم فوق‌العاده‌ای با کیفیت کار بالا. پروژه ما را در زمان مقرر و با بهترین کیفیت تحویل دادند.',
    author: 'احم<PERSON> محمدی',
    position: 'مدیرعامل شرکت تکنولوژی'
  },
  {
    text: 'خدمات عالی و پشتیبانی ۲۴ ساعته. واقعاً راضی هستیم از همکاری با این تیم.',
    author: 'فاطمه احمدی',
    position: 'بنیان‌گذار استارتاپ'
  }
];

export default function Testimonials() {
  return (
    <section className="testimonials">
      <div className="container">
        <h2 className="section-title">نظرات و تجربیات شما</h2>

        <div className="testimonials-grid">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="testimonial-card">
              <p>{testimonial.text}</p>

              <div className="testimonial-author">
                <strong>{testimonial.author}</strong>
                <span>{testimonial.position}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
