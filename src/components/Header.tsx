'use client';

import { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faHome,
  faCogs,
  faBriefcase,
  faCode,
  faUsers,
  faPhone,
  faRocket,
  faBars,
  faTimes
} from '@fortawesome/free-solid-svg-icons';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const headerHeight = 80;
      const targetPosition = element.offsetTop - headerHeight;
      window.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
      });
    }
    setIsMenuOpen(false);
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <header className={`premium-header ${isScrolled ? 'scrolled' : ''}`}>
      <div className="container">
        <div className="header-wrapper">
          {/* Brand Logo */}
          <div className="brand-logo" onClick={() => scrollToSection('home')}>
            <div className="logo-container">
              <FontAwesomeIcon icon={faRocket} className="logo-icon" />
              <div className="logo-glow"></div>
            </div>
            <div className="brand-info">
              <h1 className="company-name">آرمین افق دادگستر</h1>
              <span className="company-tagline">نوآوری در هر کد</span>
            </div>
          </div>

          {/* Main Navigation */}
          <nav className="main-navigation">
            <ul className="nav-menu">
              <li className="nav-item">
                <a
                  href="#home"
                  className="nav-link"
                  onClick={(e) => { e.preventDefault(); scrollToSection('home'); }}
                >
                  <FontAwesomeIcon icon={faHome} className="nav-icon" />
                  <span className="nav-text">خانه</span>
                  <div className="nav-indicator"></div>
                </a>
              </li>
              <li className="nav-item">
                <a
                  href="#services"
                  className="nav-link"
                  onClick={(e) => { e.preventDefault(); scrollToSection('services'); }}
                >
                  <FontAwesomeIcon icon={faCogs} className="nav-icon" />
                  <span className="nav-text">خدمات</span>
                  <div className="nav-indicator"></div>
                </a>
              </li>
              <li className="nav-item">
                <a
                  href="#portfolio"
                  className="nav-link"
                  onClick={(e) => { e.preventDefault(); scrollToSection('portfolio'); }}
                >
                  <FontAwesomeIcon icon={faBriefcase} className="nav-icon" />
                  <span className="nav-text">نمونه کارها</span>
                  <div className="nav-indicator"></div>
                </a>
              </li>
              <li className="nav-item">
                <a
                  href="#technologies"
                  className="nav-link"
                  onClick={(e) => { e.preventDefault(); scrollToSection('technologies'); }}
                >
                  <FontAwesomeIcon icon={faCode} className="nav-icon" />
                  <span className="nav-text">تکنولوژی‌ها</span>
                  <div className="nav-indicator"></div>
                </a>
              </li>
              <li className="nav-item">
                <a
                  href="#about"
                  className="nav-link"
                  onClick={(e) => { e.preventDefault(); scrollToSection('about'); }}
                >
                  <FontAwesomeIcon icon={faUsers} className="nav-icon" />
                  <span className="nav-text">درباره ما</span>
                  <div className="nav-indicator"></div>
                </a>
              </li>
            </ul>
          </nav>

          {/* CTA Section */}
          <div className="header-actions">
            <button className="cta-button" onClick={() => scrollToSection('contact')}>
              <FontAwesomeIcon icon={faPhone} className="cta-icon" />
              <span className="cta-text">تماس با ما</span>
              <div className="cta-ripple"></div>
            </button>
          </div>

          {/* Mobile Menu Button */}
          <button
            className={`mobile-menu-btn ${isMenuOpen ? 'active' : ''}`}
            onClick={toggleMenu}
            aria-label="منوی موبایل"
          >
            <FontAwesomeIcon
              icon={isMenuOpen ? faTimes : faBars}
              className="mobile-menu-icon"
            />
          </button>
        </div>

        {/* Mobile Navigation Overlay */}
        <div className={`mobile-overlay ${isMenuOpen ? 'active' : ''}`}>
          <nav className="mobile-navigation">
            <ul className="mobile-menu">
              <li className="mobile-item">
                <a
                  href="#home"
                  className="mobile-link"
                  onClick={(e) => { e.preventDefault(); scrollToSection('home'); }}
                >
                  <FontAwesomeIcon icon={faHome} className="mobile-icon" />
                  <span>خانه</span>
                </a>
              </li>
              <li className="mobile-item">
                <a
                  href="#services"
                  className="mobile-link"
                  onClick={(e) => { e.preventDefault(); scrollToSection('services'); }}
                >
                  <FontAwesomeIcon icon={faCogs} className="mobile-icon" />
                  <span>خدمات</span>
                </a>
              </li>
              <li className="mobile-item">
                <a
                  href="#portfolio"
                  className="mobile-link"
                  onClick={(e) => { e.preventDefault(); scrollToSection('portfolio'); }}
                >
                  <FontAwesomeIcon icon={faBriefcase} className="mobile-icon" />
                  <span>نمونه کارها</span>
                </a>
              </li>
              <li className="mobile-item">
                <a
                  href="#technologies"
                  className="mobile-link"
                  onClick={(e) => { e.preventDefault(); scrollToSection('technologies'); }}
                >
                  <FontAwesomeIcon icon={faCode} className="mobile-icon" />
                  <span>تکنولوژی‌ها</span>
                </a>
              </li>
              <li className="mobile-item">
                <a
                  href="#about"
                  className="mobile-link"
                  onClick={(e) => { e.preventDefault(); scrollToSection('about'); }}
                >
                  <FontAwesomeIcon icon={faUsers} className="mobile-icon" />
                  <span>درباره ما</span>
                </a>
              </li>
              <li className="mobile-item mobile-cta-item">
                <button
                  className="mobile-cta-btn"
                  onClick={() => scrollToSection('contact')}
                >
                  <FontAwesomeIcon icon={faPhone} className="mobile-cta-icon" />
                  <span>تماس با ما</span>
                </button>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </header>
  );
}
