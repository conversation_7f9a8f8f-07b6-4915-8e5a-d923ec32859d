'use client';

import { useEffect, useState } from 'react';



export default function Hero() {
  const [titleText, setTitleText] = useState('');
  const fullTitle = 'از ایده تا سودآوری، لحظه به لحظه همراه تان خواهیم بود';

  useEffect(() => {
    let i = 0;
    const typeWriter = () => {
      if (i < fullTitle.length) {
        setTitleText(fullTitle.slice(0, i + 1));
        i++;
        setTimeout(typeWriter, 50);
      }
    };

    const timer = setTimeout(typeWriter, 1000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <section id="home" className="hero">
      <div className="container">
        <div className="hero-content">
          {/* Hero Image */}
          <div className="hero-image">
            <img
              src="/Screenshot 2023-07-25 202621_prev_ui 1.png"
              alt="Programming and Development Illustration"
              className="hero-illustration"
            />
          </div>

          {/* Text Content */}
          <div className="hero-text">
            <h1>
              {titleText}
              <span className="animate-pulse">|</span>
            </h1>
            <p>
              در شرکت آرمین افق دادگستر ایده‌های شما را به راهکارهای نرم‌افزاری قدرتمند تبدیل می‌کنیم.
              با تیمی خبره و متخصص و توسعه نرم‌افزار و پلتفرم های پیشرفته، پتانسیل کسب‌وکار شما را آزاد می‌کنیم.
              شرکت شما در نوآوری و رشد دیجیتال خواهیم بود و با پشتیبانی ۲۴/۷ در کنار شما آینده را خط به خط می‌سازیم.
            </p>
            <button className="cta-button">
              دانش بنیان (به زودی)
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
