'use client';

import {
  <PERSON><PERSON><PERSON><PERSON>,
  SiR<PERSON>ct,
  SiNodedotj<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>aravel,
  SiTypescript,
  SiN<PERSON>t<PERSON>tjs,
  SiDocker
} from 'react-icons/si';

const technologies = [
  {
    name: 'Python',
    icon: SiPython,
    color: '#3776ab',
    description: 'زبان برنامه‌نویسی قدرتمند'
  },
  {
    name: 'React',
    icon: SiReact,
    color: '#61dafb',
    description: 'کتابخانه UI محبوب'
  },
  {
    name: 'Node.js',
    icon: SiNodedotjs,
    color: '#339933',
    description: 'محیط اجرای JavaScript'
  },
  {
    name: 'Flutter',
    icon: SiFlutter,
    color: '#02569b',
    description: 'فریمورک موبایل Google'
  },
  {
    name: 'Lara<PERSON>',
    icon: SiLaravel,
    color: '#ff2d20',
    description: 'فریمورک PHP قدرتمند'
  },
  {
    name: 'TypeScript',
    icon: SiTypescript,
    color: '#3178c6',
    description: 'JavaScript با تایپ'
  },
  {
    name: 'Next.js',
    icon: SiNextdotjs,
    color: '#000000',
    description: 'فریمورک React'
  },
  {
    name: 'Docker',
    icon: SiDocker,
    color: '#2496ed',
    description: 'کانتینرسازی اپلیکیشن'
  }
];

export default function Technologies() {
  return (
    <section id="technologies" className="technologies">
      <div className="container">
        <div className="tech-header">
          <h2 className="section-title">تکنولوژی‌های مورد استفاده ما</h2>
          <p className="tech-subtitle">
            ما از جدیدترین و محبوب‌ترین تکنولوژی‌های دنیا برای ساخت پروژه‌های شما استفاده می‌کنیم
          </p>
        </div>

        <div className="tech-grid">
          {technologies.map((tech, index) => {
            const IconComponent = tech.icon;
            return (
              <div key={index} className="tech-card" style={{'--tech-color': tech.color} as React.CSSProperties}>
                <div className="tech-icon">
                  <IconComponent />
                </div>
                <h3 className="tech-name">{tech.name}</h3>
                <p className="tech-description">{tech.description}</p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
