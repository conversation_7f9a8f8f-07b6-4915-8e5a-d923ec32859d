'use client';

export default function Footer() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const headerHeight = 80;
      const targetPosition = element.offsetTop - headerHeight;
      window.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
      });
    }
  };

  return (
    <footer id="contact" className="modern-footer">
      {/* Main Footer Content */}
      <div className="footer-main">
        <div className="footer-container">
          {/* Top Section - CTA */}
          <div className="footer-cta-section">
            <div className="cta-content">
              <h2 className="cta-title">آماده شروع پروژه بعدی هستید؟</h2>
              <p className="cta-subtitle">بیایید با هم چیزی فوق‌العاده بسازیم</p>
              <div className="cta-buttons-modern">
                <button className="btn-primary">
                  <span className="btn-icon">🚀</span>
                  شروع پروژه
                </button>
                <button className="btn-secondary">
                  <span className="btn-icon">💬</span>
                  مشاوره رایگان
                </button>
              </div>
            </div>
          </div>

          {/* Main Content Grid */}
          <div className="footer-grid">
            {/* Company Info */}
            <div className="footer-section company-info">
              <div className="company-logo">
                <div className="logo-icon">🔥</div>
                <div className="logo-text">
                  <h3>آرمین افق داده گستر</h3>
                  <p>نوآوری در هر کد</p>
                </div>
              </div>
              <p className="company-description">
                ما تیمی از متخصصان خلاق هستیم که ایده‌های شما را به واقعیت‌های دیجیتال تبدیل می‌کنیم.
                با تکنولوژی‌های روز دنیا و خلاقیت بی‌حد و حصر.
              </p>
              <div className="social-links">
                <a href="#" className="social-link">
                  <span>�</span>
                </a>
                <a href="#" className="social-link">
                  <span>💼</span>
                </a>
                <a href="#" className="social-link">
                  <span>📧</span>
                </a>
                <a href="#" className="social-link">
                  <span>🌐</span>
                </a>
              </div>
            </div>

            {/* Quick Links */}
            <div className="footer-section">
              <h4 className="section-title">دسترسی سریع</h4>
              <ul className="footer-links">
                <li><button onClick={() => scrollToSection('home')}>صفحه اصلی</button></li>
                <li><button onClick={() => scrollToSection('services')}>خدمات</button></li>
                <li><button onClick={() => scrollToSection('portfolio')}>نمونه کارها</button></li>
                <li><button onClick={() => scrollToSection('about')}>درباره ما</button></li>
                <li><button onClick={() => scrollToSection('contact')}>تماس با ما</button></li>
              </ul>
            </div>

            {/* Services */}
            <div className="footer-section">
              <h4 className="section-title">خدمات ما</h4>
              <ul className="footer-links">
                <li><a href="#">طراحی وب‌سایت</a></li>
                <li><a href="#">توسعه اپلیکیشن</a></li>
                <li><a href="#">سیستم‌های مدیریت</a></li>
                <li><a href="#">مشاوره فنی</a></li>
                <li><a href="#">پشتیبانی</a></li>
              </ul>
            </div>

            {/* Contact Info */}
            <div className="footer-section">
              <h4 className="section-title">تماس با ما</h4>
              <div className="contact-info">
                <div className="contact-item">
                  <span className="contact-icon">📞</span>
                  <div className="contact-details">
                    <span className="contact-label">تلفن</span>
                    <span className="contact-value">۰۲۱-۹۱۶۲۰۸۳</span>
                  </div>
                </div>
                <div className="contact-item">
                  <span className="contact-icon">📧</span>
                  <div className="contact-details">
                    <span className="contact-label">ایمیل</span>
                    <span className="contact-value"><EMAIL></span>
                  </div>
                </div>
                <div className="contact-item">
                  <span className="contact-icon">📍</span>
                  <div className="contact-details">
                    <span className="contact-label">آدرس</span>
                    <span className="contact-value">تهران، قیطریه، بهار ستان</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="footer-bottom">
        <div className="footer-container">
          <div className="bottom-content">
            <div className="copyright">
              <p>© ۲۰۲۴ آرمین افق داده گستر. تمامی حقوق محفوظ است.</p>
            </div>
            <div className="bottom-links">
              <a href="#">حریم خصوصی</a>
              <a href="#">شرایط استفاده</a>
              <a href="#">سیاست کوکی</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
