'use client';

import { useEffect, useState, useRef } from 'react';

const stats = [
  { number: 100, label: 'پروژه موفق' },
  { number: 50, label: 'مشتری راضی' },
  { number: 5, label: 'سال تجربه' }
];

export default function About() {
  const [animatedStats, setAnimatedStats] = useState(stats.map(() => 0));
  const [hasAnimated, setHasAnimated] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !hasAnimated) {
            setHasAnimated(true);

            stats.forEach((stat, index) => {
              let current = 0;
              const increment = stat.number / 50;
              const timer = setInterval(() => {
                current += increment;
                if (current >= stat.number) {
                  current = stat.number;
                  clearInterval(timer);
                }
                setAnimatedStats(prev => {
                  const newStats = [...prev];
                  newStats[index] = Math.floor(current);
                  return newStats;
                });
              }, 30);
            });
          }
        });
      },
      { threshold: 0.5 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, [hasAnimated]);

  return (
    <section id="about" ref={sectionRef} className="about">
      <div className="container">
        <div className="about-content">
          {/* Text Content */}
          <div className="about-text">
            <h2>فرآیند کار ما</h2>

            <div className="process-steps">
              <div className="process-step">
                <div className="step-number">1</div>
                <div className="step-content">
                  <h3>مشاوره و تحلیل نیازها</h3>
                  <p>در گام نخست، با گوش سپردن دقیق به ایده‌ها و چالش‌های شما، نیازها و اهداف پروژه را به صورت کامل درک می‌کنیم. این مرحله پیاد موفقیت پروژه محسوب است.</p>
                </div>
              </div>

              <div className="process-step">
                <div className="step-number">2</div>
                <div className="step-content">
                  <h3>طراحی و برنامه‌ریزی</h3>
                  <p>پس از درک نیازها یک فشرده جامع شامل طراحی UI,UX، معماری فنی و زمان‌بندی دقیق پروژه را تدوین می‌کنیم تا دیدی روشن از مسیر پیش رو داشته باشید.</p>
                </div>
              </div>

              <div className="process-step">
                <div className="step-number">3</div>
                <div className="step-content">
                  <h3>توسعه و پیاده‌سازی</h3>
                  <p>تیم متخصص ما با بهره‌گیری از آخرین تکنولوژی‌ها و بهترین شیوه‌های کدنویسی طراحی‌ها را به واقعیت تبدیل کرده و نرم‌افزار شما را توسعه می‌دهد.</p>
                </div>
              </div>

              <div className="process-step">
                <div className="step-number">4</div>
                <div className="step-content">
                  <h3>تست و تضمین کیفیت</h3>
                  <p>هر بخشی از نرم‌افزار به دقت مورد آزمایش قرار می‌گیرد تا از عملکرد بی‌نقص، امنیت بالا و انطباق کامل با نیازهای شما اطمینان حاصل شود.</p>
                </div>
              </div>

              <div className="process-step">
                <div className="step-number">5</div>
                <div className="step-content">
                  <h3>راه‌اندازی و پشتیبانی</h3>
                  <p>پس از تأیید نهایی، محصول شما با موفقیت راه‌اندازی می‌شود. ما در ادامه نیز با ارائه پشتیبانی فنی و به‌روزرسانی‌های لازم همواره کنار شما خواهیم بود.</p>
                </div>
              </div>
            </div>
          </div>

          {/* Team Illustration */}
          <div className="about-image">
            <img
              src="/perspective_matte-252-128x128 1.png"
              alt="تیم توسعه نرم‌افزار"
              className="team-illustration-img"
            />
          </div>
        </div>
      </div>
    </section>
  );
}
