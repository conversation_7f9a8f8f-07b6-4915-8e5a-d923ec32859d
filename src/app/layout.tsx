import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

// FontAwesome configuration
import { config } from '@fortawesome/fontawesome-svg-core';
import '@fortawesome/fontawesome-svg-core/styles.css';
config.autoAddCss = false;

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "شرکت برنامه‌نویسی - کامیونیتی",
  description: "از ایده تا سودآوری، لحظه به لحظه همراه تان خواهیم بود",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fa" dir="rtl">
      <head>
        <link
          href="https://fonts.googleapis.com/css2?family=Vazirmatn:wght@300;400;500;600;700&display=swap"
          rel="stylesheet"
        />
      </head>
      <body className={`${inter.variable} font-vazir antialiased`}>
        {children}
      </body>
    </html>
  );
}
